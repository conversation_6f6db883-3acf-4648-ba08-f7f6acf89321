<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\LowStockWidget;
use App\Filament\Widgets\NearExpiryWidget;
use App\Filament\Widgets\SalesChartWidget;
use App\Filament\Widgets\SalesStatsWidget;
use App\Models\SalesChannel;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\DatePicker;

use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Illuminate\Contracts\Support\Htmlable;

class Dashboard extends BaseDashboard
{
    use HasFiltersForm;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?int $navigationSort = -2;

    protected static ?string $navigationLabel = 'Dashboard';

    protected static ?string $title = 'Dashboard';

    public static function canAccess(): bool
    {
        // Explicitly allow all authenticated users to access dashboard
        return true;
    }

    public function getColumns(): int | string | array
    {
        return [
            'default' => 1,
            'lg' => 2,
            'xl' => 2,
        ];
    }

    public function getWidgets(): array
    {
        $user = auth()->user();

        // Untuk admin packing, hanya tampilkan widget operasional
        if ($user && $user->isPackingAdmin()) {
            return [
                LowStockWidget::class,
                NearExpiryWidget::class,
            ];
        }

        // Untuk owner dan store manager, tampilkan semua widget
        return [
            SalesStatsWidget::class,
            SalesChartWidget::class,
            LowStockWidget::class,
            NearExpiryWidget::class,
        ];
    }

    public function filtersForm(Form $form): Form
    {
        $user = auth()->user();

        // Admin packing tidak memerlukan filter karena hanya melihat widget operasional
        if ($user && $user->isPackingAdmin()) {
            return $form->schema([]);
        }

        // Filter lengkap untuk owner dan store manager
        return $form
            ->schema([
                Card::make()
                    ->schema([
                        DatePicker::make('startDate')
                            ->label('Tanggal Mulai')
                            ->placeholder('Pilih tanggal mulai')
                            ->native(false)
                            ->displayFormat('d M Y')
                            ->default(now()->startOfMonth()),
                        DatePicker::make('endDate')
                            ->label('Tanggal Akhir')
                            ->placeholder('Pilih tanggal akhir')
                            ->native(false)
                            ->displayFormat('d M Y')
                            ->default(now()->endOfMonth()),
                        Select::make('channel_id')
                            ->label('Saluran Penjualan')
                            ->placeholder('Semua Saluran')
                            ->options(SalesChannel::query()->pluck('name', 'id'))
                            ->searchable()
                            ->preload(),
                    ])
                    ->columns(3),
            ]);
    }

    public function getSubheading(): string | Htmlable | null
    {
        $user = auth()->user();
        $nama = $user->name;
        $tanggal = \Carbon\Carbon::now()->translatedFormat('l, j F Y');

        return "Selamat datang, {$nama} • {$tanggal}";
    }
}